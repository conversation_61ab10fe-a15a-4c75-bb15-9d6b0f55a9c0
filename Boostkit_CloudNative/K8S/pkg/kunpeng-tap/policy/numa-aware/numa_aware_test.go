/*
 * Copyright (c) 2025 Huawei Technology corp.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package numa_aware

import (
	"testing"

	"github.com/docker/docker/client"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	criv1 "k8s.io/cri-api/pkg/apis/runtime/v1"

	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/cache"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/policy"
)

const (
	// Test constants for cgroup paths
	cgroupGuaranteed = "/kubepods/guaranteed"
	cgroupBurstable  = "/kubepods/burstable"
	cgroupBestEffort = "/kubepods/besteffort"
)

// MockCache implements cache.Cache interface for testing
type MockCache struct {
	nodeResources []cache.NumaNodeResources
}

// MockInvalidContext implements HookContext but is not ContainerContext
type MockInvalidContext struct{}

func (m *MockInvalidContext) FromProxy(req interface{}) {
	// Do nothing
}

func (m *MockCache) InsertPod(id string, msg interface{}, status *cache.PodStatus) (cache.Pod, error) {
	return nil, nil
}

func (m *MockCache) DeletePod(id string) cache.Pod {
	return nil
}

func (m *MockCache) LookupPod(id string) (cache.Pod, bool) {
	return nil, false
}

func (m *MockCache) InsertContainer(containerId string, msg interface{}) (cache.Container, error) {
	return nil, nil
}

func (m *MockCache) UpdateContainerID(cacheID string, msg interface{}) (cache.Container, error) {
	return nil, nil
}

func (m *MockCache) DeleteContainer(id string) cache.Container {
	return nil
}

func (m *MockCache) LookupContainer(id string) (cache.Container, bool) {
	return nil, false
}

func (m *MockCache) GetPendingContainers() []cache.Container {
	return nil
}

func (m *MockCache) GetPods() []cache.Pod {
	return nil
}

func (m *MockCache) GetContainers() []cache.Container {
	return nil
}

func (m *MockCache) GetContainerCacheIds() []string {
	return nil
}

func (m *MockCache) GetContainerIds() []string {
	return nil
}

func (m *MockCache) RefreshPods(msg *criv1.ListPodSandboxResponse, status map[string]*cache.PodStatus) ([]cache.Pod, []cache.Pod, []cache.Container) {
	return nil, nil, nil
}

func (m *MockCache) RefreshContainers(msg *criv1.ListContainersResponse) ([]cache.Container, []cache.Container) {
	return nil, nil
}

func (m *MockCache) GetNodeResources() []cache.NumaNodeResources {
	return m.nodeResources
}

func (m *MockCache) ValidateCachedContainers(containerIds []string) []string {
	return nil
}

func (m *MockCache) CleanupStaleContainers(staleContainerIds []string) int {
	return 0
}

func (m *MockCache) LoadStoreDocker(dockerClient client.CommonAPIClient, cgroupDriver string) error {
	return nil
}

func (m *MockCache) LoadStoreContainerd(backendRuntimeServiceClient criv1.RuntimeServiceClient) error {
	return nil
}

// Helper function to create resource list
func createResourceList(cpu, memory string) *v1.ResourceList {
	resources := v1.ResourceList{}
	if cpu != "" {
		resources[v1.ResourceCPU] = resource.MustParse(cpu)
	}
	if memory != "" {
		resources[v1.ResourceMemory] = resource.MustParse(memory)
	}
	return &resources
}

// Helper function to create container context
func createContainerContext(cgroupParent string, reqCPU, limitCPU, reqMem, limitMem string) *policy.ContainerContext {
	reqResources := createResourceList(reqCPU, reqMem)
	limitResources := createResourceList(limitCPU, limitMem)

	ctx := &policy.ContainerContext{
		Request: policy.ContainerRequest{
			CgroupParent: cgroupParent,
			Resources: &policy.Resources{
				EstimatedRequirements: &v1.ResourceRequirements{
					Requests: *reqResources,
					Limits:   *limitResources,
				},
			},
		},
	}
	return ctx
}

// TestNewNumaAwarePolicy tests the creation of a new NUMA-aware policy
func TestNewNumaAwarePolicy(t *testing.T) {
	mockCache := &MockCache{}
	policy := NewNumaAwarePolicy(mockCache)

	require.NotNil(t, policy)
	assert.Equal(t, PolicyName, policy.Name())
	assert.Equal(t, PolicyDescription, policy.Description())
}

// TestNumaAwarePolicyName tests the Name method
func TestNumaAwarePolicyName(t *testing.T) {
	policy := &NumaAwarePolicy{}
	assert.Equal(t, PolicyName, policy.Name())
}

// TestNumaAwarePolicyDescription tests the Description method
func TestNumaAwarePolicyDescription(t *testing.T) {
	policy := &NumaAwarePolicy{}
	assert.Equal(t, PolicyDescription, policy.Description())
}

// TestNumaAwarePolicySetCache tests the SetCache method
func TestNumaAwarePolicySetCache(t *testing.T) {
	policy := &NumaAwarePolicy{}
	mockCache := &MockCache{}

	policy.SetCache(mockCache)
	assert.Equal(t, mockCache, policy.cache)
}

// TestPreCreateContainerHookInvalidContext tests PreCreateContainerHook with invalid context
func TestPreCreateContainerHookInvalidContext(t *testing.T) {
	mockCache := &MockCache{}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	// Test with nil context
	alloc, err := policy.PreCreateContainerHook(nil)
	assert.NoError(t, err)
	assert.Nil(t, alloc)

	// Test with wrong context type
	wrongCtx := &MockInvalidContext{}
	alloc, err = policy.PreCreateContainerHook(wrongCtx)
	assert.NoError(t, err)
	assert.Nil(t, alloc)
}

// TestPreCreateContainerHookNilResources tests PreCreateContainerHook with nil resources
func TestPreCreateContainerHookNilResources(t *testing.T) {
	mockCache := &MockCache{}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	// Test with nil resources - this should not panic and should return nil
	ctx := createContainerContext(cgroupGuaranteed, "", "", "", "")
	ctx.Request.Resources = nil // Set resources to nil to test the nil check

	alloc, err := policy.PreCreateContainerHook(ctx)
	assert.NoError(t, err)
	assert.Nil(t, alloc)

	// Test with empty resources (no EstimatedRequirements)
	emptyCtx := createContainerContext(cgroupGuaranteed, "", "", "", "")
	emptyCtx.Request.Resources.EstimatedRequirements = nil
	alloc, err = policy.PreCreateContainerHook(emptyCtx)
	assert.NoError(t, err)
	assert.Nil(t, alloc)
}

// TestPreCreateContainerHookBestEffort tests PreCreateContainerHook with BestEffort QOS
func TestPreCreateContainerHookBestEffort(t *testing.T) {
	mockCache := &MockCache{}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	// Test with BestEffort QOS (should not allocate anything)
	ctx := createContainerContext(cgroupBestEffort, "100m", "200m", "128Mi", "256Mi")

	alloc, err := policy.PreCreateContainerHook(ctx)
	assert.NoError(t, err)
	assert.Nil(t, alloc)
}

// TestPreCreateContainerHookGuaranteed tests PreCreateContainerHook with Guaranteed QOS
func TestPreCreateContainerHookGuaranteed(t *testing.T) {
	// Setup mock cache with node resources
	mockCache := &MockCache{
		nodeResources: []cache.NumaNodeResources{
			{
				CpuTotal:         4.0,
				CpuUsed:          1.0,
				CpuUsedByRequest: 0.5,
				CpuFree:          3.0,
			},
			{
				CpuTotal:         4.0,
				CpuUsed:          2.0,
				CpuUsedByRequest: 1.0,
				CpuFree:          2.0,
			},
		},
	}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	// Test with Guaranteed QOS (should allocate CPU set)
	ctx := createContainerContext(cgroupGuaranteed, "1000m", "1000m", "1Gi", "1Gi")

	alloc, err := policy.PreCreateContainerHook(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, alloc)
	assert.Equal(t, "0-3", alloc.Resources.CpusetCpus) // Should allocate to node 0 (less used)
}

// TestPreCreateContainerHookBurstable tests PreCreateContainerHook with Burstable QOS
func TestPreCreateContainerHookBurstable(t *testing.T) {
	// Setup mock cache with node resources
	mockCache := &MockCache{
		nodeResources: []cache.NumaNodeResources{
			{
				CpuTotal:         4.0,
				CpuUsed:          3.0,
				CpuUsedByRequest: 2.5,
				CpuFree:          1.0,
			},
			{
				CpuTotal:         4.0,
				CpuUsed:          1.5,
				CpuUsedByRequest: 1.0,
				CpuFree:          2.5,
			},
		},
	}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	// Test with Burstable QOS (should allocate CPU set to node with less usage)
	ctx := createContainerContext(cgroupBurstable, "500m", "1000m", "512Mi", "1Gi")

	alloc, err := policy.PreCreateContainerHook(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, alloc)
	assert.Equal(t, "4-7", alloc.Resources.CpusetCpus) // Should allocate to node 1 (less used)
}

// TestPreCreateContainerHookCPUExceedsNode tests when CPU request exceeds single node capacity
func TestPreCreateContainerHookCPUExceedsNode(t *testing.T) {
	mockCache := &MockCache{
		nodeResources: []cache.NumaNodeResources{
			{
				CpuTotal:         4.0,
				CpuUsed:          1.0,
				CpuUsedByRequest: 0.5,
				CpuFree:          3.0,
			},
		},
	}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	// Test with CPU request exceeding node capacity
	ctx := createContainerContext(cgroupGuaranteed, "5000m", "5000m", "1Gi", "1Gi")

	alloc, err := policy.PreCreateContainerHook(ctx)
	assert.NoError(t, err)
	assert.Nil(t, alloc) // Should return nil when CPU exceeds node capacity
}

// TestPreCreateContainerHookNoAvailableNode tests when no node has enough capacity
func TestPreCreateContainerHookNoAvailableNode(t *testing.T) {
	mockCache := &MockCache{
		nodeResources: []cache.NumaNodeResources{
			{
				CpuTotal:         4.0,
				CpuUsed:          3.5,
				CpuUsedByRequest: 3.8, // Almost full by request
				CpuFree:          0.5,
			},
			{
				CpuTotal:         4.0,
				CpuUsed:          3.0,
				CpuUsedByRequest: 3.9, // Almost full by request
				CpuFree:          1.0,
			},
		},
	}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	// Test with CPU request that cannot fit in any node
	ctx := createContainerContext(cgroupGuaranteed, "500m", "500m", "512Mi", "512Mi")

	alloc, err := policy.PreCreateContainerHook(ctx)
	assert.NoError(t, err)
	assert.Nil(t, alloc) // Should return nil when no node has enough capacity
}

// TestAllocateCPUSetNilInputs tests allocateCPUSet with nil inputs
func TestAllocateCPUSetNilInputs(t *testing.T) {
	mockCache := &MockCache{}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	// Test with nil request
	alloc := policy.allocateCPUSet(nil, createResourceList("1000m", "1Gi"))
	assert.Nil(t, alloc)

	// Test with nil limit
	alloc = policy.allocateCPUSet(createResourceList("1000m", "1Gi"), nil)
	assert.Nil(t, alloc)

	// Test with both nil
	alloc = policy.allocateCPUSet(nil, nil)
	assert.Nil(t, alloc)
}

// TestAllocateCPUSetEmptyNodeResources tests allocateCPUSet with empty node resources
func TestAllocateCPUSetEmptyNodeResources(t *testing.T) {
	mockCache := &MockCache{
		nodeResources: []cache.NumaNodeResources{}, // Empty node resources
	}
	policy := NewNumaAwarePolicy(mockCache).(*NumaAwarePolicy)

	request := createResourceList("1000m", "1Gi")
	limit := createResourceList("1000m", "1Gi")

	alloc := policy.allocateCPUSet(request, limit)
	assert.Nil(t, alloc) // Should return nil when no nodes available
}
